<template>
  <div class="page flex-col">
    <div class="group_1 flex-col">
      <div class="group_2 flex-row">
        <span class="text_1">限流配置</span>
        <div class="group_3 flex-row" @click="addLimitRule">
          <span class="text_2">添加限流配置</span>
        </div>
        <div class="text-wrapper_1 flex-col" @click="saveConfig">
          <span class="text_3">保存配置</span>
        </div>
      </div>

      <div class="group_4 flex-row">
        <div class="text-wrapper_2 flex-row">
          <span class="text_4">渠道</span>
          <span class="text_5">时间限制</span>
          <span class="text_6">限制条数</span>
          <span class="text_7">操作</span>
        </div>
      </div>

      <!-- 已有配置行 -->
      <div v-for="(rule, index) in limitForm.rules" :key="index" class="group_5 flex-row">
        <div class="group_6">
          <el-select v-model="rule.channel" placeholder="请选择渠道" class="channel-select">
            <el-option
              v-for="(label, value) in channels"
              :key="value"
              :label="label"
              :value="value">
            </el-option>
          </el-select>
        </div>
        <div class="text-wrapper_3 flex-col">
          <el-input-number
            v-model="rule.interval"
            :min="0"
            :controls="false"
            placeholder="数值"
            class="interval-input">
          </el-input-number>
        </div>
        <div class="group_7">
          <el-select v-model="rule.timeUnit" class="time-unit-select">
            <el-option
              v-for="(label, value) in timeUnits"
              :key="value"
              :label="label"
              :value="value">
            </el-option>
          </el-select>
        </div>
        <span class="text_11">内</span>
        <div class="text-wrapper_4 flex-row">
          <el-input-number
            v-model="rule.maxReplies"
            :min="0"
            :controls="false"
            placeholder="数值"
            class="replies-input">
          </el-input-number>
          <span class="text_13">条</span>
        </div>
        <i class="el-icon-delete thumbnail_4" @click="removeLimitRule(index)"></i>
      </div>

      <div class="image-wrapper_1 flex-row">
        <div class="image_2"></div>
      </div>



      <!-- 新增配置行 -->
      <div v-if="showAddForm" class="group_10 flex-row">
        <div class="block_1">
          <el-select v-model="newRule.channel" placeholder="请选择渠道" class="channel-select">
            <el-option
              v-for="(label, value) in channels"
              :key="value"
              :label="label"
              :value="value">
            </el-option>
          </el-select>
        </div>
        <div class="text-wrapper_7 flex-col">
          <el-input-number
            v-model="newRule.interval"
            :min="0"
            :controls="false"
            placeholder="数值"
            class="interval-input">
          </el-input-number>
        </div>
        <div class="block_2">
          <el-select v-model="newRule.timeUnit" class="time-unit-select">
            <el-option
              v-for="(label, value) in timeUnits"
              :key="value"
              :label="label"
              :value="value">
            </el-option>
          </el-select>
        </div>
        <span class="text_23">内</span>
        <div class="text-wrapper_8 flex-row">
          <el-input-number
            v-model="newRule.maxReplies"
            :min="0"
            :controls="false"
            placeholder="数值"
            class="replies-input">
          </el-input-number>
          <span class="text_25">条</span>
        </div>
        <i class="el-icon-check thumbnail_11" @click="confirmAdd"></i>
      </div>

      <div class="image-wrapper_3 flex-row">
        <div class="image_4"></div>
      </div>
    </div>


  </div>
</template>

<script>
export default {
  name: 'LimitConfig',
  data() {
    return {
      limitForm: {
        rules: [
          {
            channel: 'wechat_personal',
            interval: 60,
            timeUnit: 'second',
            maxReplies: 5
          }
        ]
      },
      showAddForm: false,
      newRule: {
        channel: '',
        interval: null,
        timeUnit: 'second',
        maxReplies: null
      },
      channels: {
        'wechat_enterprise': '微信公众号（企业）',
        'wechat_personal': '微信公众号（个人）',
        'wechat_service': '微信客服',
        'wechat': '微信',
        'enterprise_wechat': '企业微信',
        'web_embed': '网页嵌入'
      },
      timeUnits: {
        'second': '秒',
        'minute': '分钟',
        'hour': '小时',
        'day': '天'
      }
    }
  },
  methods: {
    addLimitRule() {
      this.showAddForm = true
      this.newRule = {
        channel: '',
        interval: null,
        timeUnit: 'second',
        maxReplies: null
      }
    },
    confirmAdd() {
      if (this.newRule.channel && this.newRule.interval && this.newRule.maxReplies) {
        this.limitForm.rules.push({ ...this.newRule })
        this.showAddForm = false
      }
    },
    removeLimitRule(index) {
      this.limitForm.rules.splice(index, 1)
    },
    getChannelName(channel) {
      return this.channels[channel] || '未知渠道'
    },
    getTimeUnitName(unit) {
      return this.timeUnits[unit] || '秒'
    },
    saveConfig() {
      // 保存配置逻辑
      console.log('保存配置:', this.limitForm.rules)
    }
  }
}
</script>

<style lang="css" scoped>
/* 基础布局 */
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.page {
  background-color: rgba(247, 249, 253, 1);
  position: relative;
  width: 100%;
  min-height: 100vh;
  overflow: hidden;
}

.group_1 {
  background: rgba(255, 255, 255, 1);
  border-radius: 8px;
  margin: 20px;
  padding: 32px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.group_2 {
  width: 100%;
  height: 30px;
  margin-bottom: 24px;
  align-items: center;
}

.text_1 {
  width: 64px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.group_3 {
  width: 127px;
  height: 30px;
  background: rgba(37, 109, 255, 0.1);
  border-radius: 4px;
  margin-left: auto;
  margin-right: 12px;
  cursor: pointer;
  align-items: center;
  justify-content: center;
}

.text_2 {
  width: 84px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(37, 109, 255, 1);
  font-size: 14px;
  text-align: center;
  white-space: nowrap;
  line-height: 20px;
}

.text-wrapper_1 {
  height: 30px;
  background: rgba(37, 109, 255, 1);
  border-radius: 4px;
  width: 88px;
  cursor: pointer;
  align-items: center;
  justify-content: center;
}

.text_3 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 14px;
  text-align: center;
  white-space: nowrap;
  line-height: 20px;
}

.group_4 {
  width: 100%;
  height: 44px;
  margin-bottom: 12px;
}

.text-wrapper_2 {
  width: 100%;
  height: 44px;
  background: rgba(244, 246, 248, 1);
  border-radius: 4px;
  align-items: center;
  padding: 0 24px;
}

.text_4 {
  width: 28px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(136, 136, 136, 1);
  font-size: 14px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.text_5 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(136, 136, 136, 1);
  font-size: 14px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin-left: 293px;
}

.text_6 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(136, 136, 136, 1);
  font-size: 14px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin-left: 381px;
}

.text_7 {
  width: 28px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(136, 136, 136, 1);
  font-size: 14px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin-left: 244px;
}

.group_5 {
  width: 100%;
  height: 40px;
  margin-bottom: 12px;
  align-items: center;
  padding: 0 24px;
}

.group_6 {
  width: 240px;
  height: 40px;
  align-items: center;
}

.text_8 {
  width: 126px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  flex: 1;
}

.thumbnail_2 {
  width: 8px;
  height: 13px;
  cursor: pointer;
}

.text-wrapper_3 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  height: 40px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  margin-left: 81px;
  width: 200px;
  align-items: center;
  justify-content: center;
}

.text_9 {
  width: 28px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(186, 186, 186, 1);
  font-size: 14px;
  text-align: center;
  white-space: nowrap;
  line-height: 20px;
}

.group_7 {
  width: 120px;
  height: 40px;
  margin-left: 11px;
  align-items: center;
}

.text_10 {
  width: 14px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  flex: 1;
}

.thumbnail_3 {
  width: 8px;
  height: 13px;
  cursor: pointer;
}

.text_11 {
  width: 14px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin-left: 11px;
}

.text-wrapper_4 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  width: 200px;
  height: 40px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  margin-left: 81px;
  align-items: center;
  padding: 0 16px;
}

.text_12 {
  width: 28px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(186, 186, 186, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  flex: 1;
}

.text_13 {
  width: 14px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.thumbnail_4 {
  font-size: 16px;
  color: #f56c6c;
  margin-left: 107px;
  cursor: pointer;
  padding: 4px;
}

.image-wrapper_1 {
  width: 100%;
  height: 1px;
  margin: 11px 0;
}

.image_2 {
  width: 100%;
  height: 1px;
  background-color: rgba(223, 225, 232, 1);
}

.group_8 {
  width: 100%;
  height: 64px;
  margin: 1px 0;
}

.group_9 {
  background-color: rgba(244, 246, 248, 1);
  width: 100%;
  height: 64px;
  border-radius: 4px;
}

.box_1 {
  width: calc(100% - 48px);
  height: 49px;
  margin: 12px 24px 0;
  align-items: center;
}

.box_2 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  width: 240px;
  height: 40px;
  border: 0.5px solid rgba(37, 109, 255, 1);
  align-items: center;
  padding: 0 16px;
  cursor: pointer;
}

.text_14 {
  width: 70px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(186, 186, 186, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  flex: 1;
}

.thumbnail_5 {
  width: 13px;
  height: 7px;
  cursor: pointer;
}

.text-wrapper_5 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  height: 40px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  margin-left: 81px;
  width: 200px;
  align-items: center;
  justify-content: center;
}

.text_15 {
  width: 100%;
  height: 20px;
  border: none;
  outline: none;
  background: transparent;
  color: rgba(186, 186, 186, 1);
  font-size: 14px;
  text-align: center;
  line-height: 20px;
}

.box_3 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  width: 120px;
  height: 40px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  margin-left: 11px;
  align-items: center;
  padding: 0 16px;
  cursor: pointer;
}

.text_16 {
  width: 14px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  flex: 1;
}

.thumbnail_6 {
  width: 8px;
  height: 13px;
  cursor: pointer;
}

.text_17 {
  width: 14px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin-left: 11px;
}

.text-wrapper_6 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  width: 200px;
  height: 40px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  margin-left: 81px;
  align-items: center;
  padding: 0 16px;
}

.text_18 {
  width: 100%;
  height: 20px;
  border: none;
  outline: none;
  background: transparent;
  color: rgba(186, 186, 186, 1);
  font-size: 14px;
  text-align: left;
  line-height: 20px;
  flex: 1;
}

.text_19 {
  width: 14px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.image-wrapper_2 {
  width: 20px;
  height: 36px;
  margin-left: 107px;
}

.thumbnail_7 {
  width: 12px;
  height: 13px;
  cursor: pointer;
  margin-bottom: 6px;
}

.thumbnail_8 {
  width: 14px;
  height: 17px;
  cursor: pointer;
}

.image_3 {
  width: 100%;
  height: 1px;
  background-color: rgba(223, 225, 232, 1);
  margin-top: 2px;
}

.group_10 {
  width: 100%;
  height: 40px;
  margin: 12px 0;
  align-items: center;
  padding: 0 24px;
}

.block_1 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  width: 240px;
  height: 40px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  align-items: center;
  padding: 0 16px;
  cursor: pointer;
}

.text_20 {
  width: 70px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(186, 186, 186, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  flex: 1;
}

.thumbnail_9 {
  width: 8px;
  height: 13px;
  cursor: pointer;
}

.text-wrapper_7 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  height: 40px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  margin-left: 81px;
  width: 200px;
  align-items: center;
  justify-content: center;
}

.text_21 {
  width: 100%;
  height: 20px;
  border: none;
  outline: none;
  background: transparent;
  color: rgba(186, 186, 186, 1);
  font-size: 14px;
  text-align: center;
  line-height: 20px;
}

.block_2 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  width: 120px;
  height: 40px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  margin-left: 11px;
  align-items: center;
  padding: 0 16px;
  cursor: pointer;
}

.text_22 {
  width: 14px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  flex: 1;
}

.thumbnail_10 {
  width: 8px;
  height: 13px;
  cursor: pointer;
}

.text_23 {
  width: 14px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin-left: 11px;
}

.text-wrapper_8 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  width: 200px;
  height: 40px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  margin-left: 81px;
  align-items: center;
  padding: 0 16px;
}

.text_24 {
  width: 100%;
  height: 20px;
  border: none;
  outline: none;
  background: transparent;
  color: rgba(186, 186, 186, 1);
  font-size: 14px;
  text-align: left;
  line-height: 20px;
  flex: 1;
}

.text_25 {
  width: 14px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.thumbnail_11 {
  font-size: 16px;
  color: #67c23a;
  margin-left: 107px;
  cursor: pointer;
  padding: 4px;
}

.image-wrapper_3 {
  width: 100%;
  height: 1px;
  margin: 12px 0 96px;
}

.image_4 {
  width: 100%;
  height: 1px;
  background-color: rgba(223, 225, 232, 1);
}

.group_13 {
  box-shadow: 0px 0px 10px 0px rgba(190, 190, 190, 0.3);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  height: 252px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  width: 240px;
  position: absolute;
  left: 80px;
  top: 330px;
  z-index: 1000;
}

.text-wrapper_13 {
  width: 126px;
  height: 20px;
  margin: 16px 0 0 20px;
}

.text_33 {
  width: 126px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.group_14 {
  width: 227px;
  height: 108px;
  margin: 12px 0 0 8px;
}

.group_15 {
  width: 220px;
  height: 108px;
}

.text-wrapper_14 {
  background-color: rgba(244, 246, 248, 1);
  border-radius: 4px;
  height: 36px;
  width: 220px;
  align-items: center;
  padding: 0 12px;
  cursor: pointer;
}

.text_34 {
  width: 126px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(37, 109, 255, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.text_35 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 12px 0 0 12px;
  cursor: pointer;
}

.text_36 {
  width: 28px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 20px 0 0 12px;
  cursor: pointer;
}

.group_16 {
  background-color: rgba(217, 217, 217, 1);
  border-radius: 3px;
  width: 3px;
  height: 60px;
  margin-top: 31px;
}

.text-wrapper_15 {
  width: 56px;
  height: 20px;
  margin: 20px 0 0 20px;
}

.text_37 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  cursor: pointer;
}

.text-wrapper_16 {
  width: 56px;
  height: 20px;
  margin: 20px 0 16px 20px;
}

.text_38 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  cursor: pointer;
}

/* 时间单位下拉框样式 */
.time-unit-dropdown {
  box-shadow: 0px 0px 10px 0px rgba(190, 190, 190, 0.3);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  width: 120px;
  position: absolute;
  left: 400px;
  top: 200px;
  z-index: 1000;
  padding: 8px 0;
}

.time-unit-option {
  height: 32px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.time-unit-option:hover {
  background-color: rgba(244, 246, 248, 1);
}

.time-unit-option span {
  font-size: 14px;
  color: rgba(0, 0, 0, 1);
  line-height: 20px;
}

/* Element UI 组件样式调整 */
.channel-select {
  width: 240px;
}

.channel-select :deep(.el-input__inner) {
  height: 40px;
  border-radius: 8px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  font-size: 14px;
  padding: 0 16px;
}

.time-unit-select {
  width: 120px;
}

.time-unit-select :deep(.el-input__inner) {
  height: 40px;
  border-radius: 8px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  font-size: 14px;
  padding: 0 16px;
}

.interval-input {
  width: 200px;
}

.interval-input :deep(.el-input__inner) {
  height: 40px;
  border-radius: 8px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  font-size: 14px;
  text-align: center;
}

.replies-input {
  width: 160px;
}

.replies-input :deep(.el-input__inner) {
  height: 40px;
  border-radius: 8px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  font-size: 14px;
  text-align: left;
}

/* 更新容器样式 */
.block_1 {
  width: 240px;
  height: 40px;
  align-items: center;
}

.block_2 {
  width: 120px;
  height: 40px;
  margin-left: 11px;
  align-items: center;
}
</style>
