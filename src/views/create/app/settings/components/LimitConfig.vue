<template>
  <div class="page flex-col">
    <div class="group_1 flex-col">
      <div class="group_2 flex-row">
        <span class="text_1">限流配置</span>
        <div class="group_3 flex-row" @click="addLimitRule">
          <span class="text_2">添加限流配置</span>
        </div>
        <div class="text-wrapper_1 flex-col" @click="saveConfig">
          <span class="text_3">保存配置</span>
        </div>
      </div>

      <div class="group_4 flex-row">
        <div class="text-wrapper_2 flex-row">
          <span class="text_4">渠道</span>
          <span class="text_5">时间限制</span>
          <span class="text_6">限制条数</span>
          <span class="text_7">操作</span>
        </div>
      </div>

      <!-- 已有配置行 -->
      <div v-for="(rule, index) in limitForm.rules" :key="index" class="group_5 flex-row">
        <div class="group_6 flex-row" @click="editRule(index)">
          <span class="text_8">{{ getChannelName(rule.channel) }}</span>
          <img class="thumbnail_2" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOCIgaGVpZ2h0PSIxMyIgdmlld0JveD0iMCAwIDggMTMiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNyA2LjVMMSAxMiIgc3Ryb2tlPSIjOTk5IiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=" />
        </div>
        <div class="text-wrapper_3 flex-col">
          <input type="number" v-model="rule.interval" class="text_9" @blur="updateRule(index)" />
        </div>
        <div class="group_7 flex-row" @click="showTimeUnitDropdownForRule(index)">
          <span class="text_10">{{ getTimeUnitName(rule.timeUnit) }}</span>
          <img class="thumbnail_3" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOCIgaGVpZ2h0PSIxMyIgdmlld0JveD0iMCAwIDggMTMiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNyA2LjVMMSAxMiIgc3Ryb2tlPSIjOTk5IiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=" />
        </div>
        <span class="text_11">内</span>
        <div class="text-wrapper_4 flex-row">
          <input type="number" v-model="rule.maxReplies" class="text_12" @blur="updateRule(index)" />
          <span class="text_13">条</span>
        </div>
        <i class="el-icon-delete thumbnail_4" @click="removeLimitRule(index)"></i>
      </div>

      <div class="image-wrapper_1 flex-row">
        <div class="image_2"></div>
      </div>

      <!-- 编辑中的配置行 -->
      <div v-if="showEditForm" class="group_8 flex-row">
        <div class="group_9 flex-col justify-end">
          <div class="box_1 flex-row">
            <div class="box_2 flex-row" @click="showChannelDropdown = !showChannelDropdown">
              <span class="text_14">{{ editingRule.channel ? getChannelName(editingRule.channel) : '请选择渠道' }}</span>
              <img class="thumbnail_5" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTMiIGhlaWdodD0iNyIgdmlld0JveD0iMCAwIDEzIDciIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNi41IDZMMTIgMSIgc3Ryb2tlPSIjMjU2REZGIiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=" />
            </div>
            <div class="text-wrapper_5 flex-col">
              <input type="number" v-model="editingRule.interval" class="text_15" placeholder="数值" />
            </div>
            <div class="box_3 flex-row" @click="showTimeUnitDropdown = !showTimeUnitDropdown">
              <span class="text_16">{{ getTimeUnitName(editingRule.timeUnit) }}</span>
              <img class="thumbnail_6" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOCIgaGVpZ2h0PSIxMyIgdmlld0JveD0iMCAwIDggMTMiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNyA2LjVMMSAxMiIgc3Ryb2tlPSIjOTk5IiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=" />
            </div>
            <span class="text_17">内</span>
            <div class="text-wrapper_6 flex-row">
              <input type="number" v-model="editingRule.maxReplies" class="text_18" placeholder="数值" />
              <span class="text_19">条</span>
            </div>
            <div class="image-wrapper_2 flex-col justify-between">
              <img class="thumbnail_7" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTMiIHZpZXdCb3g9IjAgMCAxMiAxMyIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEgMUwxMSA2LjVMMSAxMiIgc3Ryb2tlPSIjMjU2REZGIiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=" @click="confirmEdit" />
              <img class="thumbnail_8" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTQiIGhlaWdodD0iMTciIHZpZXdCb3g9IjAgMCAxNCAxNyIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEgMUwxMyA4LjVMMSAxNiIgc3Ryb2tlPSIjRkY0NDQ0IiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=" @click="cancelEdit" />
            </div>
          </div>
          <div class="image_3"></div>
        </div>
      </div>

      <!-- 新增配置行 -->
      <div v-if="showAddForm" class="group_10 flex-row">
        <div class="block_1 flex-row" @click="showNewChannelDropdown = !showNewChannelDropdown">
          <span class="text_20">{{ newRule.channel ? getChannelName(newRule.channel) : '请选择渠道' }}</span>
          <img class="thumbnail_9" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOCIgaGVpZ2h0PSIxMyIgdmlld0JveD0iMCAwIDggMTMiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNyA2LjVMMSAxMiIgc3Ryb2tlPSIjOTk5IiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=" />
        </div>
        <div class="text-wrapper_7 flex-col">
          <input type="number" v-model="newRule.interval" class="text_21" placeholder="数值" />
        </div>
        <div class="block_2 flex-row" @click="showNewTimeUnitDropdown = !showNewTimeUnitDropdown">
          <span class="text_22">{{ getTimeUnitName(newRule.timeUnit) }}</span>
          <img class="thumbnail_10" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOCIgaGVpZ2h0PSIxMyIgdmlld0JveD0iMCAwIDggMTMiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNyA2LjVMMSAxMiIgc3Ryb2tlPSIjOTk5IiBzdHJva2Utd2lkdGg9IjEuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=" />
        </div>
        <span class="text_23">内</span>
        <div class="text-wrapper_8 flex-row">
          <input type="number" v-model="newRule.maxReplies" class="text_24" placeholder="数值" />
          <span class="text_25">条</span>
        </div>
        <i class="el-icon-check thumbnail_11" @click="confirmAdd"></i>
      </div>

      <div class="image-wrapper_3 flex-row">
        <div class="image_4"></div>
      </div>
    </div>

    <!-- 渠道选择下拉框 -->
    <div v-if="showChannelDropdown || showNewChannelDropdown || showRuleChannelDropdown" class="group_13 flex-col">
      <div class="text-wrapper_13 flex-row" @click="selectChannel('wechat_enterprise')">
        <span class="text_33">微信公众号（企业）</span>
      </div>
      <div class="group_14 flex-row justify-between">
        <div class="group_15 flex-col">
          <div class="text-wrapper_14 flex-col" @click="selectChannel('wechat_personal')">
            <span class="text_34">微信公众号（个人）</span>
          </div>
          <span class="text_35" @click="selectChannel('wechat_service')">微信客服</span>
          <span class="text_36" @click="selectChannel('wechat')">微信</span>
        </div>
        <div class="group_16 flex-col"></div>
      </div>
      <div class="text-wrapper_15 flex-row">
        <span class="text_37" @click="selectChannel('enterprise_wechat')">企业微信</span>
      </div>
      <div class="text-wrapper_16 flex-row">
        <span class="text_38" @click="selectChannel('web_embed')">网页嵌入</span>
      </div>
    </div>

    <!-- 时间单位选择下拉框 -->
    <div v-if="showTimeUnitDropdown || showNewTimeUnitDropdown || showRuleTimeUnitDropdown" class="time-unit-dropdown flex-col">
      <div class="time-unit-option" @click="selectTimeUnit('second')">
        <span>秒</span>
      </div>
      <div class="time-unit-option" @click="selectTimeUnit('minute')">
        <span>分钟</span>
      </div>
      <div class="time-unit-option" @click="selectTimeUnit('hour')">
        <span>小时</span>
      </div>
      <div class="time-unit-option" @click="selectTimeUnit('day')">
        <span>天</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LimitConfig',
  data() {
    return {
      limitForm: {
        rules: [
          {
            channel: 'wechat_personal',
            interval: 60,
            timeUnit: 'second',
            maxReplies: 5
          }
        ]
      },
      showAddForm: false,
      showEditForm: false,
      showChannelDropdown: false,
      showTimeUnitDropdown: false,
      showNewChannelDropdown: false,
      showNewTimeUnitDropdown: false,
      showRuleChannelDropdown: false,
      showRuleTimeUnitDropdown: false,
      currentEditingRuleIndex: -1,
      newRule: {
        channel: '',
        interval: null,
        timeUnit: 'second',
        maxReplies: null
      },
      editingRule: {
        channel: '',
        interval: null,
        timeUnit: 'second',
        maxReplies: null
      },
      editingIndex: -1,
      channels: {
        'wechat_enterprise': '微信公众号（企业）',
        'wechat_personal': '微信公众号（个人）',
        'wechat_service': '微信客服',
        'wechat': '微信',
        'enterprise_wechat': '企业微信',
        'web_embed': '网页嵌入'
      },
      timeUnits: {
        'second': '秒',
        'minute': '分钟',
        'hour': '小时',
        'day': '天'
      }
    }
  },
  methods: {
    addLimitRule() {
      this.showAddForm = true
      this.newRule = {
        channel: '',
        interval: null,
        timeUnit: 'second',
        maxReplies: null
      }
    },
    confirmAdd() {
      if (this.newRule.channel && this.newRule.interval && this.newRule.maxReplies) {
        this.limitForm.rules.push({ ...this.newRule })
        this.showAddForm = false
        this.showNewChannelDropdown = false
        this.showNewTimeUnitDropdown = false
      }
    },
    removeLimitRule(index) {
      this.limitForm.rules.splice(index, 1)
    },
    editRule(index) {
      this.currentEditingRuleIndex = index
      this.showRuleChannelDropdown = true
      // 关闭其他下拉框
      this.showChannelDropdown = false
      this.showNewChannelDropdown = false
      this.showNewTimeUnitDropdown = false
      this.showTimeUnitDropdown = false
      this.showRuleTimeUnitDropdown = false
    },
    confirmEdit() {
      if (this.editingRule.channel && this.editingRule.interval && this.editingRule.maxReplies) {
        this.limitForm.rules[this.editingIndex] = { ...this.editingRule }
        this.showEditForm = false
        this.showChannelDropdown = false
        this.showTimeUnitDropdown = false
        this.editingIndex = -1
      }
    },
    cancelEdit() {
      this.showEditForm = false
      this.showChannelDropdown = false
      this.showTimeUnitDropdown = false
      this.editingIndex = -1
    },
    selectChannel(channel) {
      if (this.showNewChannelDropdown) {
        this.newRule.channel = channel
        this.showNewChannelDropdown = false
      } else if (this.showChannelDropdown) {
        this.editingRule.channel = channel
        this.showChannelDropdown = false
      } else if (this.showRuleChannelDropdown && this.currentEditingRuleIndex >= 0) {
        this.limitForm.rules[this.currentEditingRuleIndex].channel = channel
        this.showRuleChannelDropdown = false
        this.currentEditingRuleIndex = -1
      }
    },
    selectTimeUnit(unit) {
      if (this.showNewTimeUnitDropdown) {
        this.newRule.timeUnit = unit
        this.showNewTimeUnitDropdown = false
      } else if (this.showTimeUnitDropdown) {
        this.editingRule.timeUnit = unit
        this.showTimeUnitDropdown = false
      } else if (this.showRuleTimeUnitDropdown && this.currentEditingRuleIndex >= 0) {
        this.limitForm.rules[this.currentEditingRuleIndex].timeUnit = unit
        this.showRuleTimeUnitDropdown = false
        this.currentEditingRuleIndex = -1
      }
    },
    showTimeUnitDropdownForRule(index) {
      this.currentEditingRuleIndex = index
      this.showRuleTimeUnitDropdown = true
      // 关闭其他下拉框
      this.showChannelDropdown = false
      this.showNewChannelDropdown = false
      this.showNewTimeUnitDropdown = false
      this.showTimeUnitDropdown = false
      this.showRuleChannelDropdown = false
    },
    updateRule(index) {
      // 当输入框失去焦点时更新规则
      console.log('更新规则:', index, this.limitForm.rules[index])
    },
    getChannelName(channel) {
      return this.channels[channel] || '未知渠道'
    },
    getTimeUnitName(unit) {
      return this.timeUnits[unit] || '秒'
    },
    saveConfig() {
      // 保存配置逻辑
      console.log('保存配置:', this.limitForm.rules)
    }
  }
}
</script>

<style lang="css" scoped>
/* 基础布局 */
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.page {
  background-color: rgba(247, 249, 253, 1);
  position: relative;
  width: 100%;
  min-height: 100vh;
  overflow: hidden;
}

.group_1 {
  background: rgba(255, 255, 255, 1);
  border-radius: 8px;
  margin: 20px;
  padding: 32px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.group_2 {
  width: 100%;
  height: 30px;
  margin-bottom: 24px;
  align-items: center;
}

.text_1 {
  width: 64px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.group_3 {
  width: 127px;
  height: 30px;
  background: rgba(37, 109, 255, 0.1);
  border-radius: 4px;
  margin-left: auto;
  margin-right: 12px;
  cursor: pointer;
  align-items: center;
  justify-content: center;
}

.text_2 {
  width: 84px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(37, 109, 255, 1);
  font-size: 14px;
  text-align: center;
  white-space: nowrap;
  line-height: 20px;
}

.text-wrapper_1 {
  height: 30px;
  background: rgba(37, 109, 255, 1);
  border-radius: 4px;
  width: 88px;
  cursor: pointer;
  align-items: center;
  justify-content: center;
}

.text_3 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 14px;
  text-align: center;
  white-space: nowrap;
  line-height: 20px;
}

.group_4 {
  width: 100%;
  height: 44px;
  margin-bottom: 12px;
}

.text-wrapper_2 {
  width: 100%;
  height: 44px;
  background: rgba(244, 246, 248, 1);
  border-radius: 4px;
  align-items: center;
  padding: 0 24px;
}

.text_4 {
  width: 28px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(136, 136, 136, 1);
  font-size: 14px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.text_5 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(136, 136, 136, 1);
  font-size: 14px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin-left: 293px;
}

.text_6 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(136, 136, 136, 1);
  font-size: 14px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin-left: 381px;
}

.text_7 {
  width: 28px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(136, 136, 136, 1);
  font-size: 14px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin-left: 244px;
}

.group_5 {
  width: 100%;
  height: 40px;
  margin-bottom: 12px;
  align-items: center;
  padding: 0 24px;
}

.group_6 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  width: 240px;
  height: 40px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  align-items: center;
  padding: 0 16px;
}

.text_8 {
  width: 126px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  flex: 1;
}

.thumbnail_2 {
  width: 8px;
  height: 13px;
  cursor: pointer;
}

.text-wrapper_3 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  height: 40px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  margin-left: 81px;
  width: 200px;
  align-items: center;
  justify-content: center;
}

.text_9 {
  width: 28px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(186, 186, 186, 1);
  font-size: 14px;
  text-align: center;
  white-space: nowrap;
  line-height: 20px;
}

.group_7 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  width: 120px;
  height: 40px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  margin-left: 11px;
  align-items: center;
  padding: 0 16px;
}

.text_10 {
  width: 14px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  flex: 1;
}

.thumbnail_3 {
  width: 8px;
  height: 13px;
  cursor: pointer;
}

.text_11 {
  width: 14px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin-left: 11px;
}

.text-wrapper_4 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  width: 200px;
  height: 40px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  margin-left: 81px;
  align-items: center;
  padding: 0 16px;
}

.text_12 {
  width: 28px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(186, 186, 186, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  flex: 1;
}

.text_13 {
  width: 14px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.thumbnail_4 {
  font-size: 16px;
  color: #f56c6c;
  margin-left: 107px;
  cursor: pointer;
  padding: 4px;
}

.image-wrapper_1 {
  width: 100%;
  height: 1px;
  margin: 11px 0;
}

.image_2 {
  width: 100%;
  height: 1px;
  background-color: rgba(223, 225, 232, 1);
}

.group_8 {
  width: 100%;
  height: 64px;
  margin: 1px 0;
}

.group_9 {
  background-color: rgba(244, 246, 248, 1);
  width: 100%;
  height: 64px;
  border-radius: 4px;
}

.box_1 {
  width: calc(100% - 48px);
  height: 49px;
  margin: 12px 24px 0;
  align-items: center;
}

.box_2 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  width: 240px;
  height: 40px;
  border: 0.5px solid rgba(37, 109, 255, 1);
  align-items: center;
  padding: 0 16px;
  cursor: pointer;
}

.text_14 {
  width: 70px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(186, 186, 186, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  flex: 1;
}

.thumbnail_5 {
  width: 13px;
  height: 7px;
  cursor: pointer;
}

.text-wrapper_5 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  height: 40px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  margin-left: 81px;
  width: 200px;
  align-items: center;
  justify-content: center;
}

.text_15 {
  width: 100%;
  height: 20px;
  border: none;
  outline: none;
  background: transparent;
  color: rgba(186, 186, 186, 1);
  font-size: 14px;
  text-align: center;
  line-height: 20px;
}

.box_3 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  width: 120px;
  height: 40px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  margin-left: 11px;
  align-items: center;
  padding: 0 16px;
  cursor: pointer;
}

.text_16 {
  width: 14px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  flex: 1;
}

.thumbnail_6 {
  width: 8px;
  height: 13px;
  cursor: pointer;
}

.text_17 {
  width: 14px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin-left: 11px;
}

.text-wrapper_6 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  width: 200px;
  height: 40px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  margin-left: 81px;
  align-items: center;
  padding: 0 16px;
}

.text_18 {
  width: 100%;
  height: 20px;
  border: none;
  outline: none;
  background: transparent;
  color: rgba(186, 186, 186, 1);
  font-size: 14px;
  text-align: left;
  line-height: 20px;
  flex: 1;
}

.text_19 {
  width: 14px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.image-wrapper_2 {
  width: 20px;
  height: 36px;
  margin-left: 107px;
}

.thumbnail_7 {
  width: 12px;
  height: 13px;
  cursor: pointer;
  margin-bottom: 6px;
}

.thumbnail_8 {
  width: 14px;
  height: 17px;
  cursor: pointer;
}

.image_3 {
  width: 100%;
  height: 1px;
  background-color: rgba(223, 225, 232, 1);
  margin-top: 2px;
}

.group_10 {
  width: 100%;
  height: 40px;
  margin: 12px 0;
  align-items: center;
  padding: 0 24px;
}

.block_1 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  width: 240px;
  height: 40px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  align-items: center;
  padding: 0 16px;
  cursor: pointer;
}

.text_20 {
  width: 70px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(186, 186, 186, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  flex: 1;
}

.thumbnail_9 {
  width: 8px;
  height: 13px;
  cursor: pointer;
}

.text-wrapper_7 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  height: 40px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  margin-left: 81px;
  width: 200px;
  align-items: center;
  justify-content: center;
}

.text_21 {
  width: 100%;
  height: 20px;
  border: none;
  outline: none;
  background: transparent;
  color: rgba(186, 186, 186, 1);
  font-size: 14px;
  text-align: center;
  line-height: 20px;
}

.block_2 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  width: 120px;
  height: 40px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  margin-left: 11px;
  align-items: center;
  padding: 0 16px;
  cursor: pointer;
}

.text_22 {
  width: 14px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  flex: 1;
}

.thumbnail_10 {
  width: 8px;
  height: 13px;
  cursor: pointer;
}

.text_23 {
  width: 14px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin-left: 11px;
}

.text-wrapper_8 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  width: 200px;
  height: 40px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  margin-left: 81px;
  align-items: center;
  padding: 0 16px;
}

.text_24 {
  width: 100%;
  height: 20px;
  border: none;
  outline: none;
  background: transparent;
  color: rgba(186, 186, 186, 1);
  font-size: 14px;
  text-align: left;
  line-height: 20px;
  flex: 1;
}

.text_25 {
  width: 14px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.thumbnail_11 {
  font-size: 16px;
  color: #67c23a;
  margin-left: 107px;
  cursor: pointer;
  padding: 4px;
}

.image-wrapper_3 {
  width: 100%;
  height: 1px;
  margin: 12px 0 96px;
}

.image_4 {
  width: 100%;
  height: 1px;
  background-color: rgba(223, 225, 232, 1);
}

.group_13 {
  box-shadow: 0px 0px 10px 0px rgba(190, 190, 190, 0.3);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  height: 252px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  width: 240px;
  position: absolute;
  left: 80px;
  top: 330px;
  z-index: 1000;
}

.text-wrapper_13 {
  width: 126px;
  height: 20px;
  margin: 16px 0 0 20px;
}

.text_33 {
  width: 126px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.group_14 {
  width: 227px;
  height: 108px;
  margin: 12px 0 0 8px;
}

.group_15 {
  width: 220px;
  height: 108px;
}

.text-wrapper_14 {
  background-color: rgba(244, 246, 248, 1);
  border-radius: 4px;
  height: 36px;
  width: 220px;
  align-items: center;
  padding: 0 12px;
  cursor: pointer;
}

.text_34 {
  width: 126px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(37, 109, 255, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.text_35 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 12px 0 0 12px;
  cursor: pointer;
}

.text_36 {
  width: 28px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 20px 0 0 12px;
  cursor: pointer;
}

.group_16 {
  background-color: rgba(217, 217, 217, 1);
  border-radius: 3px;
  width: 3px;
  height: 60px;
  margin-top: 31px;
}

.text-wrapper_15 {
  width: 56px;
  height: 20px;
  margin: 20px 0 0 20px;
}

.text_37 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  cursor: pointer;
}

.text-wrapper_16 {
  width: 56px;
  height: 20px;
  margin: 20px 0 16px 20px;
}

.text_38 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  cursor: pointer;
}

/* 时间单位下拉框样式 */
.time-unit-dropdown {
  box-shadow: 0px 0px 10px 0px rgba(190, 190, 190, 0.3);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  border: 0.5px solid rgba(223, 225, 232, 1);
  width: 120px;
  position: absolute;
  left: 400px;
  top: 200px;
  z-index: 1000;
  padding: 8px 0;
}

.time-unit-option {
  height: 32px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.time-unit-option:hover {
  background-color: rgba(244, 246, 248, 1);
}

.time-unit-option span {
  font-size: 14px;
  color: rgba(0, 0, 0, 1);
  line-height: 20px;
}

/* 输入框样式调整 */
.text_9 {
  width: 100%;
  height: 20px;
  border: none;
  outline: none;
  background: transparent;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: center;
  line-height: 20px;
}

.text_12 {
  width: 100%;
  height: 20px;
  border: none;
  outline: none;
  background: transparent;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  text-align: left;
  line-height: 20px;
  flex: 1;
}
</style>
