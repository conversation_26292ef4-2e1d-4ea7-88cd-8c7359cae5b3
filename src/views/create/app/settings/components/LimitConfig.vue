# 限流配置组件
<template>
  <div class="settings-content">
    <div class="header-actions">
      <el-button type="primary" icon="el-icon-plus" @click="addLimitRule">新增配置</el-button>
    </div>

    <div class="limit-card-container">
      <div v-for="(rule, index) in limitForm.rules" :key="index" class="limit-card">
        <div class="card-header">
          <h3>按使用频率</h3>
          <div class="header-actions">
            <el-button type="primary" icon="el-icon-document-copy">保存</el-button>
            <el-button type="danger" icon="el-icon-delete" @click="removeLimitRule(index)">删除</el-button>
          </div>
        </div>

        <el-form :model="rule" label-width="0" class="limit-form">
          <div class="limit-row">
            <el-select v-model="rule.scope" placeholder="全部" style="width: 120px">
              <el-option label="全部" value="all" />
              <el-option label="指定用户" value="specific" />
              <el-option label="指定角色" value="role" />
            </el-select>
            <span class="limit-text">渠道，单用户每</span>
            <el-input-number
              v-model="rule.interval"
              :min="0"
              controls-position="right"
              style="width: 100px"
            />
            <el-select v-model="rule.timeUnit" style="width: 80px">
              <el-option label="秒" value="second" />
              <el-option label="分钟" value="minute" />
              <el-option label="小时" value="hour" />
              <el-option label="天" value="day" />
            </el-select>
            <span class="limit-text">，只能回复</span>
            <el-input-number
              v-model="rule.maxReplies"
              :min="0"
              controls-position="right"
              style="width: 100px"
            />
            <span class="limit-text">条。</span>
          </div>

          <div class="timeout-section">
            <div class="section-header">
              <h4>超出限制时回复：</h4>
            </div>
            <el-input
              type="textarea"
              v-model="rule.timeoutReply"
              :rows="3"
              placeholder="默认不回复"
            />
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LimitConfig',
  data() {
    return {
      limitForm: {
        rules: [
          {
            scope: 'all',
            interval: 0,
            timeUnit: 'second',
            maxReplies: 0,
            timeoutReply: ''
          }
        ]
      }
    }
  },
  methods: {
    addLimitRule() {
      this.limitForm.rules.push({
        scope: 'all',
        interval: 0,
        timeUnit: 'second',
        maxReplies: 0,
        timeoutReply: ''
      })
    },
    removeLimitRule(index) {
      this.limitForm.rules.splice(index, 1)
    }
  }
}
</script>

<style lang="scss" scoped>
.settings-content {
  padding: 20px;
  background: #fff;
  height: calc(100% - 80px);

  .header-actions {
    text-align: right;
  }
}

.limit-card-container {
  height: calc(100% - 80px);
  overflow-y: auto;
  margin-top: 8px;
}

.limit-card {
  background: #fff;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 8px;

  :last-child {
    margin-bottom: 0;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: #1f2937;
    }

    .header-actions {
      display: flex;
      gap: 8px;
    }
  }
}

.limit-form {
  .limit-row {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;

    .limit-text {
      color: #606266;
    }

    :deep(.el-input-number) {
      width: 80px;

      .el-input__inner {
        text-align: center;
      }
    }

    :deep(.el-select) {
      .el-input__inner {
        text-align: center;
      }
    }
  }
}

.timeout-section {
  .section-header {
    margin-bottom: 6px;

    h4 {
      margin: 0;
      font-size: 14px;
      font-weight: normal;
      color: #606266;
    }
  }
}
</style>